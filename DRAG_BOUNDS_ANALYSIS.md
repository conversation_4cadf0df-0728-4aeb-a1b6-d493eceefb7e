# Análise do Problema de Limites de Drag

## Problema Identificado

Os elementos podem ser arrastados para fora da área editável visível, mesmo que os limites estejam sendo aplicados. O problema está na diferença entre o sistema de coordenadas visual e o sistema de coordenadas lógico.

## Estrutura Atual

### Canvas Layout

```
Canvas (100% do container)
├── Rect (área editável visual) - x: offsetX, y: offsetY
└── Group (container dos elementos) - x: offsetX, y: offsetY
    ├── Elemento 1 - coordenadas relativas ao Group
    ├── Elemento 2 - coordenadas relativas ao Group
    └── Transformer - coordenadas relativas ao Group
```

### Sistema de Coordenadas

1. **Canvas**: Ocupa todo o espaço disponível (containerWidth x containerHeight)
2. **Área Editável Visual**: Centralizada no canvas com offset
3. **Group**: Posicionado no mesmo offset da área editável
4. **Elementos**: Coordenadas relativas ao Group (0,0 = canto superior esquerdo do Group)

## Problema Raiz

O `dragBoundFunc` está recebendo as dimensões corretas da área editável escalada, mas há uma inconsistência entre:

- As coordenadas dos elementos (relativas ao Group)
- Os limites calculados (baseados na área editável)

## Soluções Possíveis

### Solução 1: Ajustar dragBoundFunc (Atual)

- Usar dimensões da área editável escalada
- Coordenadas relativas ao Group (0,0 a editableAreaWidth x editableAreaHeight)

### Solução 2: Validação no onDragEnd

- Permitir drag livre durante o movimento
- Aplicar limites no momento do onDragEnd
- Mais flexível e menos restritivo durante o drag

### Solução 3: Coordenadas Absolutas

- Remover o Group offset
- Usar coordenadas absolutas do canvas
- Ajustar todos os cálculos para coordenadas absolutas

## Implementação da Solução 1 (Escolhida)

### Verificações Necessárias

1. ✅ editableAreaWidth e editableAreaHeight são passados corretamente
2. ✅ Dimensões são escaladas no shape-utils.tsx
3. ✅ dragBoundFunc usa as dimensões corretas
4. ❓ Verificar se os valores estão sendo aplicados corretamente

### Debug Steps

1. Verificar valores passados para dragBoundFunc
2. Confirmar se maxWidth e maxHeight estão corretos
3. Testar com valores fixos para confirmar lógica
4. Comparar com área visual

## Correções Implementadas

### 1. Sistema de Coordenadas Corrigido

- ✅ Removido `dragBoundFunc` temporariamente para testar
- ✅ Corrigido `handleDragEnd` para usar coordenadas corretas
- ✅ Reintroduzido `dragBoundFunc` com lógica correta

### 2. Logs de Debug Adicionados

- ✅ Log em `renderShape` para verificar dimensões passadas
- ✅ Log em `dragBoundFunc` para verificar limites aplicados
- ✅ Verificação de valores em tempo real

### 3. Validação Dupla

- ✅ `dragBoundFunc` restringe movimento durante o drag
- ✅ `handleDragEnd` aplica limites finais nas coordenadas

## Próximos Passos

1. ✅ Implementar logs temporários para debug
2. 🔄 Verificar valores em tempo real (em andamento)
3. ⏳ Ajustar lógica baseado nos logs
4. ⏳ Testar com diferentes tamanhos de tela
5. ⏳ Remover logs de debug

## Teste Atual

Com os logs implementados, agora é possível:

1. Abrir o console do navegador
2. Arrastar um elemento na área editável
3. Verificar os valores nos logs:
     - `renderShape debug`: Mostra as dimensões calculadas
     - `dragBoundFunc debug`: Mostra os limites sendo aplicados

## Análise Esperada

Os logs devem mostrar:

- `editableAreaWidth` e `editableAreaHeight`: Dimensões não escaladas da apresentação
- `scaledEditableWidth` e `scaledEditableHeight`: Dimensões escaladas
- `maxWidth` e `maxHeight` no dragBoundFunc: Devem corresponder às dimensões escaladas
- `pos`: Coordenadas atuais do elemento durante o drag
- `result`: Coordenadas limitadas que serão aplicadas
