import { HTMLOverlayProps } from '@/pages/edit-apresentation/types/html-overlay/container.type';
import { itemsAtom } from '@/shared/states/items/object-item.state';
import { useAtomValue } from 'jotai';
import { componentRegistry } from './components';
import { HTMLOverlayItem } from './item';

export function HTMLOverlay({ scale, containerSize, zoomPan }: HTMLOverlayProps) {
	const items = useAtomValue(itemsAtom);
	const renderableItems = items.filter((item) => item.type in componentRegistry).sort((a, b) => b.layer - a.layer);

	// O HTMLOverlay deve sincronizar com as transformações do stage do Konva
	// As transformações são aplicadas via CSS para manter a sincronização
	const combinedScale = zoomPan ? scale * zoomPan.zoom : scale;
	const transform = zoomPan ? `translate(${zoomPan.pan.x}px, ${zoomPan.pan.y}px) scale(${zoomPan.zoom})` : undefined;

	return (
		<div
			style={{
				pointerEvents: 'none',
				transform,
				transformOrigin: '0 0',
			}}
		>
			{renderableItems.map((item) => (
				<HTMLOverlayItem key={item.tempId} item={item} scale={scale} containerSize={containerSize} />
			))}
		</div>
	);
}
