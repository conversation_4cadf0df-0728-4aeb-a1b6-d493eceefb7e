export const OverlayContainer = ({
	canvasProps,
	children,
	padding = 0,
	editableAreaWidth,
	editableAreaHeight,
	scale,
}: {
	canvasProps: { width: number; height: number };
	children: React.ReactNode;
	padding?: number;
	editableAreaWidth?: number;
	editableAreaHeight?: number;
	scale?: number;
}) => {
	// Calcular offset para centralizar a área editável
	const scaledEditableWidth = editableAreaWidth && scale ? editableAreaWidth * scale : canvasProps.width - padding * 2;
	const scaledEditableHeight = editableAreaHeight && scale ? editableAreaHeight * scale : canvasProps.height - padding * 2;
	const offsetX = (canvasProps.width - scaledEditableWidth) / 2;
	const offsetY = (canvasProps.height - scaledEditableHeight) / 2;

	return (
		<div
			style={{
				position: 'absolute',
				top: 0,
				left: 0,
				width: canvasProps.width,
				height: canvasProps.height,
				pointerEvents: 'none',
				zIndex: 1,
			}}
		>
			<div
				style={{
					position: 'absolute',
					backgroundColor: 'rgba(128, 128, 128, 0.05)',
					top: offsetY,
					left: offsetX,
					width: scaledEditableWidth,
					height: scaledEditableHeight,
					pointerEvents: 'none',
				}}
			>
				{children}
			</div>
		</div>
	);
};
