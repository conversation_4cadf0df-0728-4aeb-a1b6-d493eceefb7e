import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, useDisclosure } from '@nextui-org/react';
import { HelpCircle, Keyboard } from 'lucide-react';

export const ZoomHelp = () => {
	const { isOpen, onOpen, onOpenChange } = useDisclosure();

	const shortcuts = [
		{
			category: 'Zoom',
			items: [
				{ keys: ['Ctrl/Cmd', '+'], description: 'Zoom In' },
				{ keys: ['Ctrl/Cmd', '-'], description: 'Zoom Out' },
				{ keys: ['Ctrl/Cmd', '0'], description: 'Reset Zoom & Pan' },
				{ keys: ['Mouse Wheel'], description: 'Zoom at cursor position' },
			],
		},
		{
			category: '<PERSON>',
			items: [
				{ keys: ['Space', '+', 'Mouse Drag'], description: 'Pan around canvas' },
				{ keys: ['Ctrl', '+', 'Mouse Drag'], description: 'Pan around canvas' },
				{ keys: ['Middle Mouse', '+', 'Drag'], description: 'Pan around canvas' },
				{ keys: ['Arrow Keys'], description: 'Pan in small steps' },
				{ keys: ['Alt', '+', 'Arrow Keys'], description: 'Pan in large steps' },
			],
		},
		{
			category: 'Elements',
			items: [
				{ keys: ['Arrow Keys'], description: 'Move selected elements' },
				{ keys: ['Shift', '+', 'Arrow Keys'], description: 'Move elements faster' },
				{ keys: ['Delete'], description: 'Delete selected elements' },
				{ keys: ['Ctrl/Cmd', 'A'], description: 'Select all elements' },
			],
		},
	];

	return (
		<>
			<Button
				isIconOnly
				size="sm"
				variant="flat"
				onPress={onOpen}
				title="Keyboard Shortcuts"
				className="absolute bottom-4 left-4 z-10 bg-background/80 backdrop-blur-sm"
			>
				<HelpCircle size={16} />
			</Button>

			<Modal isOpen={isOpen} onOpenChange={onOpenChange} size="2xl">
				<ModalContent>
					{(onClose) => (
						<>
							<ModalHeader className="flex items-center gap-2">
								<Keyboard size={20} />
								Canvas Navigation & Controls
							</ModalHeader>
							<ModalBody className="pb-6">
								<div className="space-y-6">
									{shortcuts.map((category) => (
										<div key={category.category}>
											<h3 className="mb-3 text-lg font-semibold text-foreground">
												{category.category}
											</h3>
											<div className="space-y-2">
												{category.items.map((item, index) => (
													<div
														key={index}
														className="flex items-center justify-between rounded-lg bg-default-100 p-3"
													>
														<span className="text-sm text-foreground-600">
															{item.description}
														</span>
														<div className="flex items-center gap-1">
															{item.keys.map((key, keyIndex) => (
																<span key={keyIndex} className="flex items-center gap-1">
																	<kbd className="rounded bg-default-200 px-2 py-1 text-xs font-mono text-foreground">
																		{key}
																	</kbd>
																	{keyIndex < item.keys.length - 1 && (
																		<span className="text-xs text-foreground-400">+</span>
																	)}
																</span>
															))}
														</div>
													</div>
												))}
											</div>
										</div>
									))}
								</div>
								<div className="mt-6 rounded-lg bg-warning-50 p-4 dark:bg-warning-50/10">
									<p className="text-sm text-warning-600 dark:text-warning-400">
										<strong>Tip:</strong> Focus the canvas area (click on it) to use keyboard shortcuts.
										Pan mode is activated while holding Space or Ctrl key.
									</p>
								</div>
							</ModalBody>
						</>
					)}
				</ModalContent>
			</Modal>
		</>
	);
};
