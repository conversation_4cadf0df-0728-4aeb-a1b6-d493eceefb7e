import { But<PERSON> } from '@nextui-org/react';
import { Minus, Plus, RotateCcw } from 'lucide-react';
import { IUseCanvasZoomPan } from '../../../hooks/canvas/config/features/zoom-pan.hook';

interface ZoomControlsProps {
	zoomPan: IUseCanvasZoomPan;
}

export const ZoomControls = ({ zoomPan }: ZoomControlsProps) => {
	const zoomPercentage = Math.round(zoomPan.zoom * 100);

	return (
		<div className="absolute bottom-4 right-4 z-10 flex flex-col gap-2 rounded-lg bg-background/80 p-2 shadow-lg backdrop-blur-sm">
			<Button
				isIconOnly
				size="sm"
				variant="flat"
				onPress={zoomPan.zoomIn}
				title="Zoom In (Ctrl/Cmd + +)"
			>
				<Plus size={16} />
			</Button>
			
			<div className="flex items-center justify-center px-2 py-1 text-xs font-medium text-foreground">
				{zoomPercentage}%
			</div>
			
			<Button
				isIconOnly
				size="sm"
				variant="flat"
				onPress={zoomPan.zoomOut}
				title="Zoom Out (Ctrl/Cmd + -)"
			>
				<Minus size={16} />
			</Button>
			
			<Button
				isIconOnly
				size="sm"
				variant="flat"
				onPress={zoomPan.resetAll}
				title="Reset Zoom & Pan (Ctrl/Cmd + 0)"
			>
				<RotateCcw size={16} />
			</Button>
		</div>
	);
};
