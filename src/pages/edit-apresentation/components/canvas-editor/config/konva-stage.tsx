import Konva from 'konva';
import React, { forwardRef } from 'react';
import { Stage } from 'react-konva';

interface KonvaStageProps {
	width: number;
	height: number;
	onStageClick: (e: Konva.KonvaEventObject<MouseEvent>) => void;
	onMouseMove: (e: Konva.KonvaEventObject<MouseEvent>) => void;
	onMouseDown?: (e: Konva.KonvaEventObject<MouseEvent>) => void;
	onMouseLeave?: (e: Konva.KonvaEventObject<MouseEvent>) => void;
	onMouseUp?: (e: Konva.KonvaEventObject<MouseEvent>) => void;
	onWheel?: (e: Konva.KonvaEventObject<WheelEvent>) => void;
	children: React.ReactNode;
}

const KonvaStage = forwardRef<Konva.Stage, KonvaStageProps>(
	({ width, height, onStageClick, onMouseDown, onMouseMove, onMouseLeave, onMouseUp, onWheel, children }, ref) => {
		const handleMouseMove = (e: Konva.KonvaEventObject<MouseEvent>) => {
			if (onMouseMove) onMouseMove(e);
			const stage = e.target.getStage?.();
			if (stage) {
				const isDragging = stage.isDragging();
				if (isDragging) {
					stage.clearCache();
				}
			}
		};

		const handleMouseLeave = (e: Konva.KonvaEventObject<MouseEvent>) => {
			if (onMouseLeave) onMouseLeave(e);
			const stage = e.target.getStage?.();
			if (stage) {
				stage.clearCache();
				stage.getLayers().forEach((layer: any) => {
					layer.clearCache();
					layer.draw();
				});
				stage.draw();
			}
		};

		return (
			<Stage
				ref={ref}
				width={width}
				height={height}
				onClick={onStageClick}
				onMouseDown={onMouseDown}
				onMouseMove={handleMouseMove}
				onMouseLeave={handleMouseLeave}
				onMouseUp={onMouseUp}
				onWheel={onWheel}
				className="focus:outline-none"
				style={{ position: 'relative', zIndex: 2 }}
				perfectDrawEnabled={false}
				clearBeforeDraw={true}
				draggable={false}
			>
				{children}
			</Stage>
		);
	},
);

export default KonvaStage;
