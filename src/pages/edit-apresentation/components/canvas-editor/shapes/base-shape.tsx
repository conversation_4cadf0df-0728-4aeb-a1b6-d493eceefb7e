import { useCanvasCacheManager } from '@/pages/edit-apresentation/hooks/canvas/cache/canvas-cache-manager.hook';
import { IShapeConfiguration } from '@/pages/edit-apresentation/types/canvas/shape';
import { Rect } from 'react-konva';

export const clamp = (value: number, min: number, max: number): number => Math.min(Math.max(value, min), max);

export const BaseCanvasRectangle: React.FC<IShapeConfiguration> = ({
	id,
	x,
	y,
	width,
	height,
	isSelected,
	onClick,
	onDragStart,
	onDragEnd,
	onTransformEnd,
	canvasWidth,
	canvasHeight,
	onMouseMove,
	onMouseLeave,
	currentlyHoveredItem,
	onDragMove,
	opacity: customOpacity,
	stroke: customStroke,
	strokeWidth: customStrokeWidth,
	fill: customFill,
	padding = 0,
	editableAreaWidth,
	editableAreaHeight,
}) => {
	const cacheManager = useCanvasCacheManager({ enableDebugLogs: false });
	const isHovered = currentlyHoveredItem?.tempId === id;
	const strokeWidth = customStrokeWidth ?? (isSelected ? 2 : isHovered ? 3 : 0);
	const opacity = customOpacity ?? (isHovered && !isSelected ? 0.8 : 0.15);
	const stroke = customStroke ?? (isSelected || isHovered ? 'green' : 'black');
	const fill = customFill ?? (isSelected ? 'green' : 'transparent');

	const dragBoundFunc = (pos: { x: number; y: number }) => {
		// Os elementos estão dentro de um Group com offset, então as coordenadas
		// são relativas ao Group, não ao canvas completo
		// Usar as dimensões da área editável escalada
		const maxWidth = editableAreaWidth || canvasWidth;
		const maxHeight = editableAreaHeight || canvasHeight;

		// Log temporário para debug
		console.log('dragBoundFunc debug:', {
			elementId: id,
			pos,
			maxWidth,
			maxHeight,
			width,
			height,
			editableAreaWidth,
			canvasWidth,
			result: {
				x: clamp(Math.round(pos.x), 0, maxWidth - width),
				y: clamp(Math.round(pos.y), 0, maxHeight - height),
			},
		});

		return {
			x: clamp(Math.round(pos.x), 0, maxWidth - width),
			y: clamp(Math.round(pos.y), 0, maxHeight - height),
		};
	};

	// Handlers melhorados para limpeza de cache
	const handleDragStart = (e: any) => {
		if (onDragStart) onDragStart();

		// Usar o gerenciador de cache para início de drag
		const stage = e.target.getStage();
		cacheManager.onDragStartCleanup(stage);
	};

	const handleDragMove = (e: any) => {
		if (onDragMove) onDragMove({ id, event: e });

		// Usar o gerenciador de cache para movimento
		const stage = e.target.getStage();
		cacheManager.onDragMoveCleanup(stage);
	};

	const handleDragEnd = (e: any) => {
		if (onDragEnd) onDragEnd(e);

		// Usar o gerenciador de cache para fim de drag
		const stage = e.target.getStage();
		cacheManager.onDragEndCleanup(stage);
	};

	return (
		<Rect
			id={id}
			x={Math.round(x)}
			key={id}
			y={Math.round(y)}
			width={Math.round(width)}
			height={Math.round(height)}
			opacity={opacity}
			stroke={stroke}
			fill={customOpacity ? 'transparent' : fill}
			strokeWidth={strokeWidth}
			draggable={isSelected}
			onClick={onClick}
			perfectDrawEnabled={!isSelected}
			listening
			onDragStart={handleDragStart}
			onDragMove={handleDragMove}
			onDragEnd={handleDragEnd}
			onMouseMove={onMouseMove}
			onMouseLeave={onMouseLeave}
			onTransformEnd={onTransformEnd}
			dragBoundFunc={dragBoundFunc}
		/>
	);
};
