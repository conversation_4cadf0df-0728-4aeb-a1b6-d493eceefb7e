import { IShapeRenderOptions } from '@/pages/edit-apresentation/types/canvas/shape';
import { IItem } from '@/pages/edit-apresentation/types/item.type';
import { BaseCanvasRectangle } from './base-shape';

const getScaledDimensions = (position: { x: number; y: number }, size: { width: number; height: number }, scale: number) => ({
	x: position.x * scale,
	y: position.y * scale,
	width: size.width * scale,
	height: size.height * scale,
});

export function renderShape(item: IItem, { scale, padding, editableAreaWidth, editableAreaHeight, ...eventHandlers }: IShapeRenderOptions): React.ReactElement {
	const { tempId: id, position, size } = item;
	const dimensions = getScaledDimensions(position, size, scale);

	const scaledEditableWidth = editableAreaWidth ? editableAreaWidth * scale : undefined;
	const scaledEditableHeight = editableAreaHeight ? editableAreaHeight * scale : undefined;

	// Debug log temporário
	console.log('renderShape debug:', {
		itemId: id,
		editableAreaWidth,
		editableAreaHeight,
		scale,
		scaledEditableWidth,
		scaledEditableHeight,
		dimensions,
	});

	const transformedEventHandlers = {
		...eventHandlers,
		currentlyHoveredItem: eventHandlers.currentlyHoveredItem
			? {
					name: eventHandlers.currentlyHoveredItem.name,
					width: eventHandlers.currentlyHoveredItem.width,
					height: eventHandlers.currentlyHoveredItem.height,
					tempId: eventHandlers.currentlyHoveredItem.tempId,
				}
			: null,
		padding,
		editableAreaWidth: scaledEditableWidth,
		editableAreaHeight: scaledEditableHeight,
	};

	return <BaseCanvasRectangle id={id} {...dimensions} {...transformedEventHandlers} />;
}
