import { ITEM_SHAPE_TYPE } from '../item.type';
import { IUseCanvasZoomPan } from '../../hooks/canvas/config/features/zoom-pan.hook';

export interface PositionHtmlOverlay {
	x: number;
	y: number;
}

export interface SizeInHtmlOverlay {
	width: number;
	height: number;
}

export interface ContainerSize extends SizeInHtmlOverlay {}

export interface Item {
	tempId: string;
	position: PositionHtmlOverlay;
	size: SizeInHtmlOverlay;
	layer: number;
	isDragging?: boolean;
	type: (typeof ITEM_SHAPE_TYPE)[keyof typeof ITEM_SHAPE_TYPE];
	content?: string;
}

export interface HTMLOverlayProps {
	scale: number;
	containerSize: ContainerSize;
	zoomPan?: IUseCanvasZoomPan;
}
