import { IElementToAdd } from '@/shared/states/items/next-items.state';
import { itemsAtom, selectedItemsIdsAtom } from '@/shared/states/items/object-item.state';
import { useAtom } from 'jotai';
import Konva from 'konva';
import { KonvaEventObject } from 'konva/lib/Node';
import { useState } from 'react';
import { useCanvasClick, useDragAndDrop, useShapeSelection, useSnapLines, useTransformHandler } from '..';
import { IUseCanvasZoomPan } from '../../config/features/zoom-pan.hook';

export function useCanvasInteractions(props: {
	nextElement: IElementToAdd | null;
	setNextElement: React.Dispatch<React.SetStateAction<IElementToAdd | null>>;
	transformerRef: React.RefObject<Konva.Transformer>;
	scale: number;
	canvasWidth: number;
	canvasHeight: number;
	editableAreaWidth: number;
	editableAreaHeight: number;
	padding?: number;
	zoomPan: IUseCanvasZoomPan;
}) {
	const {
		nextElement,
		setNextElement,
		scale,
		canvasWidth,
		canvasHeight,
		editableAreaWidth,
		editableAreaHeight,
		transformerRef,
		padding = 10,
		zoomPan,
	} = props;
	const [items, setItems] = useAtom(itemsAtom);
	const [selectedsIds, setSelectedsIds] = useAtom(selectedItemsIdsAtom);
	const [hoverPosition, setHoverPosition] = useState<{ x: number; y: number } | null>(null);
	const { clearSnapLines, checkSnapLines } = useSnapLines(items, scale, canvasWidth, canvasHeight);

	const { handleCanvasClick } = useCanvasClick({
		nextElement,
		scale,
		canvasWidth,
		canvasHeight,
		items,
		setItems,
		setNextElement,
		setSelectedsIds,
		padding,
		editableAreaWidth: canvasWidth,
		editableAreaHeight: canvasHeight,
	});

	const { handleSelectShape } = useShapeSelection({ setSelectedsIds });

	const { handleDragStart, handleDragEnd, handleDragMove } = useDragAndDrop({
		items,
		setItems,
		selectedsIds,
		scale,
		canvasWidth,
		canvasHeight,
		editableAreaWidth,
		editableAreaHeight,
		clearSnapLines,
		checkSnapLines,
	});

	const { handleTransformStart, handleTransformEnd, handleTransform } = useTransformHandler({
		items,
		setItems,
		selectedsIds,
		scale,
		transformerRef,
		clearSnapLines,
		checkSnapLines,
		editableAreaWidth: canvasWidth,
		editableAreaHeight: canvasHeight,
	});

	const handleMouseMove = (event: KonvaEventObject<MouseEvent>) => {
		if (!event.target) return;
		const stage = event.target.getStage?.();
		if (!stage) return;
		const pos = stage.getPointerPosition();
		if (pos) {
			setHoverPosition({ x: pos.x, y: pos.y });
		}

		// Handle pan move if panning
		if (zoomPan.isPanning) {
			zoomPan.handlePanMove(event);
		}
	};

	const handleMouseLeave = () => {
		setHoverPosition(null);
		zoomPan.handlePanEnd();
	};

	return {
		hoverPosition,
		handleCanvasClick,
		handleMouseMove,
		handleSelectShape,
		handleDragStart,
		handleDragEnd,
		handleTransform,
		handleDragMove,
		handleTransformStart,
		handleTransformEnd,
		transformerRef,
		handleMouseLeave,
		zoomPan,
	};
}
