import { IItem } from '@/pages/edit-apresentation/types/item.type';
import Konva from 'konva';
import { useCallback } from 'react';
import { useCanvasCacheManager } from '../../cache/canvas-cache-manager.hook';

interface UseDragAndDropProps {
	items: IItem[];
	setItems: (items: IItem[]) => void;
	selectedsIds: string[];
	scale: number;
	canvasWidth: number;
	canvasHeight: number;
	editableAreaWidth: number;
	editableAreaHeight: number;
	clearSnapLines: () => void;
	checkSnapLines: (currentItem: IItem) => void;
}

const clamp = (value: number, min: number, max: number): number => Math.min(Math.max(value, min), max);

export function useDragAndDrop({
	items,
	setItems,
	selectedsIds,
	scale,
	canvasWidth,
	canvasHeight,
	editableAreaWidth,
	editableAreaHeight,
	clearSnapLines,
	checkSnapLines,
}: UseDragAndDropProps) {
	const cacheManager = useCanvasCacheManager({ enableDebugLogs: false });

	const handleDragStart = useCallback(
		({ id }: { id: string }) => {
			if (!selectedsIds.includes(id)) return;
			setItems(items.map((item) => (selectedsIds.includes(item.tempId) ? { ...item, isDragging: true } : item)));
		},
		[selectedsIds, setItems, items],
	);

	const handleDragEnd = useCallback(
		({ id, event }: { id: string; event: Konva.KonvaEventObject<MouseEvent> }) => {
			const { x, y } = event.target.position();
			// As coordenadas x,y já vêm em valores escalados relativos ao Group
			// Converter para coordenadas não escaladas (lógicas)
			const realX = x / scale;
			const realY = y / scale;

			// Aplicar limites diretamente nas coordenadas finais
			const draggedItem = items.find((item) => item.tempId === id);
			if (!draggedItem) return;

			const updatePosition = (item: IItem) => {
				// Usar as coordenadas reais calculadas e aplicar limites
				const newX = clamp(realX, 0, editableAreaWidth - item.size.width);
				const newY = clamp(realY, 0, editableAreaHeight - item.size.height);
				return { ...item, position: { x: newX, y: newY }, isDragging: false };
			};

			setItems(items.map((item) => (selectedsIds.includes(item.tempId) ? updatePosition(item) : item)));
			clearSnapLines();

			// Usar o gerenciador de cache para limpeza completa
			const stage = event.target.getStage();
			if (stage) {
				cacheManager.onDragEndCleanup(stage);
			}
		},
		[items, setItems, scale, editableAreaWidth, editableAreaHeight, selectedsIds, clearSnapLines, cacheManager],
	);

	const handleDragMove = useCallback(
		({ id, event }: { id: string; event: Konva.KonvaEventObject<MouseEvent> }) => {
			const { x, y } = event.target.position();
			const currentItem = items.find((item) => item.tempId === id);
			if (!currentItem) return;

			const draggedItem = {
				...currentItem,
				position: { x: x / scale, y: y / scale },
			};

			checkSnapLines(draggedItem);
			const stage = event.target.getStage();
			if (stage) {
				cacheManager.onDragMoveCleanup(stage);
			}
		},
		[items, scale, checkSnapLines, cacheManager],
	);

	return { handleDragStart, handleDragEnd, handleDragMove };
}
