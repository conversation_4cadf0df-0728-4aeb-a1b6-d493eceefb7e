import { useCallback, useRef, useState } from 'react';
import Konva from 'konva';

export interface IZoomPanState {
	zoom: number;
	pan: { x: number; y: number };
}

export interface IZoomPanControls {
	zoomIn: () => void;
	zoomOut: () => void;
	resetZoom: () => void;
	resetPan: () => void;
	resetAll: () => void;
	setZoom: (zoom: number) => void;
	setPan: (pan: { x: number; y: number }) => void;
}

export interface IZoomPanTransforms {
	transformPoint: (point: { x: number; y: number }) => { x: number; y: number };
	inverseTransformPoint: (point: { x: number; y: number }) => { x: number; y: number };
	getTransformedBounds: () => { x: number; y: number; width: number; height: number };
}

export interface IZoomPanHandlers {
	handleWheel: (e: Konva.KonvaEventObject<WheelEvent>) => void;
	handlePanStart: (e: Konva.KonvaEventObject<MouseEvent>) => void;
	handlePanMove: (e: Konva.KonvaEventObject<MouseEvent>) => void;
	handlePanEnd: () => void;
}

export interface IUseCanvasZoomPan extends IZoomPanState, IZoomPanControls, IZoomPanTransforms, IZoomPanHandlers {
	isPanning: boolean;
	canPan: boolean;
	setCanPan: (canPan: boolean) => void;
}

interface UseCanvasZoomPanProps {
	stageRef: React.RefObject<Konva.Stage>;
	canvasWidth: number;
	canvasHeight: number;
	editableAreaWidth: number;
	editableAreaHeight: number;
	padding: number;
	minZoom?: number;
	maxZoom?: number;
	zoomStep?: number;
}

export const useCanvasZoomPan = ({
	stageRef,
	canvasWidth,
	canvasHeight,
	editableAreaWidth,
	editableAreaHeight,
	padding,
	minZoom = 0.1,
	maxZoom = 5,
	zoomStep = 0.1,
}: UseCanvasZoomPanProps): IUseCanvasZoomPan => {
	const [zoom, setZoomState] = useState(1);
	const [pan, setPanState] = useState({ x: 0, y: 0 });
	const [isPanning, setIsPanning] = useState(false);
	const [canPan, setCanPan] = useState(false);
	const lastPanPoint = useRef<{ x: number; y: number } | null>(null);

	// Função para aplicar transformações ao stage
	const applyTransformToStage = useCallback(
		(newZoom: number, newPan: { x: number; y: number }) => {
			const stage = stageRef.current;
			if (!stage) return;

			// Calcular o centro da área editável
			const scaledEditableWidth = editableAreaWidth * newZoom;
			const scaledEditableHeight = editableAreaHeight * newZoom;
			const centerX = canvasWidth / 2;
			const centerY = canvasHeight / 2;

			// Aplicar transformações ao stage
			stage.scale({ x: newZoom, y: newZoom });
			stage.position({
				x: centerX - scaledEditableWidth / 2 + newPan.x,
				y: centerY - scaledEditableHeight / 2 + newPan.y,
			});
			stage.batchDraw();
		},
		[stageRef, editableAreaWidth, editableAreaHeight, canvasWidth, canvasHeight],
	);

	// Controles de zoom
	const zoomIn = useCallback(() => {
		const newZoom = Math.min(zoom + zoomStep, maxZoom);
		setZoomState(newZoom);
		applyTransformToStage(newZoom, pan);
	}, [zoom, zoomStep, maxZoom, pan, applyTransformToStage]);

	const zoomOut = useCallback(() => {
		const newZoom = Math.max(zoom - zoomStep, minZoom);
		setZoomState(newZoom);
		applyTransformToStage(newZoom, pan);
	}, [zoom, zoomStep, minZoom, pan, applyTransformToStage]);

	const setZoom = useCallback(
		(newZoom: number) => {
			const clampedZoom = Math.max(minZoom, Math.min(maxZoom, newZoom));
			setZoomState(clampedZoom);
			applyTransformToStage(clampedZoom, pan);
		},
		[minZoom, maxZoom, pan, applyTransformToStage],
	);

	const setPan = useCallback(
		(newPan: { x: number; y: number }) => {
			// Calcular limites de panorâmica baseados no zoom atual
			const scaledWidth = canvasWidth * zoom;
			const scaledHeight = canvasHeight * zoom;

			// Limites para evitar que o conteúdo saia completamente da vista
			const maxPanX = Math.max(0, (scaledWidth - canvasWidth) / 2);
			const maxPanY = Math.max(0, (scaledHeight - canvasHeight) / 2);

			const clampedPan = {
				x: Math.max(-maxPanX, Math.min(maxPanX, newPan.x)),
				y: Math.max(-maxPanY, Math.min(maxPanY, newPan.y)),
			};

			setPanState(clampedPan);
			applyTransformToStage(zoom, clampedPan);
		},
		[zoom, canvasWidth, canvasHeight, applyTransformToStage],
	);

	// Reset functions
	const resetZoom = useCallback(() => {
		setZoom(1);
	}, [setZoom]);

	const resetPan = useCallback(() => {
		setPan({ x: 0, y: 0 });
	}, [setPan]);

	const resetAll = useCallback(() => {
		setZoomState(1);
		setPanState({ x: 0, y: 0 });
		applyTransformToStage(1, { x: 0, y: 0 });
	}, [applyTransformToStage]);

	// Transformações de coordenadas
	const transformPoint = useCallback(
		(point: { x: number; y: number }) => ({
			x: point.x * zoom + pan.x,
			y: point.y * zoom + pan.y,
		}),
		[zoom, pan],
	);

	const inverseTransformPoint = useCallback(
		(point: { x: number; y: number }) => ({
			x: (point.x - pan.x) / zoom,
			y: (point.y - pan.y) / zoom,
		}),
		[zoom, pan],
	);

	const getTransformedBounds = useCallback(
		() => ({
			x: pan.x,
			y: pan.y,
			width: canvasWidth * zoom,
			height: canvasHeight * zoom,
		}),
		[pan, canvasWidth, canvasHeight, zoom],
	);

	// Handlers de eventos
	const handleWheel = useCallback(
		(e: Konva.KonvaEventObject<WheelEvent>) => {
			e.evt.preventDefault();

			const stage = stageRef.current;
			if (!stage) return;

			const pointer = stage.getPointerPosition();
			if (!pointer) return;

			// Calcular novo zoom
			const scaleBy = 1.05;
			const oldScale = zoom;
			const newScale = e.evt.deltaY > 0 ? oldScale / scaleBy : oldScale * scaleBy;
			const clampedScale = Math.max(minZoom, Math.min(maxZoom, newScale));

			// Calcular novo pan para manter o ponto do mouse fixo
			const mousePointTo = {
				x: (pointer.x - pan.x) / oldScale,
				y: (pointer.y - pan.y) / oldScale,
			};

			const newPan = {
				x: pointer.x - mousePointTo.x * clampedScale,
				y: pointer.y - mousePointTo.y * clampedScale,
			};

			setZoomState(clampedScale);
			setPan(newPan);
		},
		[zoom, pan, minZoom, maxZoom, stageRef, setPan],
	);

	const handlePanStart = useCallback(
		(e: Konva.KonvaEventObject<MouseEvent>) => {
			if (!canPan) return;

			const stage = stageRef.current;
			if (!stage) return;

			const pointer = stage.getPointerPosition();
			if (!pointer) return;

			setIsPanning(true);
			lastPanPoint.current = pointer;
		},
		[canPan, stageRef],
	);

	const handlePanMove = useCallback(
		(e: Konva.KonvaEventObject<MouseEvent>) => {
			if (!isPanning || !lastPanPoint.current) return;

			const stage = stageRef.current;
			if (!stage) return;

			const pointer = stage.getPointerPosition();
			if (!pointer) return;

			const dx = pointer.x - lastPanPoint.current.x;
			const dy = pointer.y - lastPanPoint.current.y;

			setPan({
				x: pan.x + dx,
				y: pan.y + dy,
			});

			lastPanPoint.current = pointer;
		},
		[isPanning, pan, setPan, stageRef],
	);

	const handlePanEnd = useCallback(() => {
		setIsPanning(false);
		lastPanPoint.current = null;
	}, []);

	return {
		zoom,
		pan,
		isPanning,
		canPan,
		setCanPan,
		zoomIn,
		zoomOut,
		resetZoom,
		resetPan,
		resetAll,
		setZoom,
		setPan,
		transformPoint,
		inverseTransformPoint,
		getTransformedBounds,
		handleWheel,
		handlePanStart,
		handlePanMove,
		handlePanEnd,
	};
};
