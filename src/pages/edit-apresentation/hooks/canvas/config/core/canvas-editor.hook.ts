import { presentationInfoAtom } from '@/pages/edit-apresentation/states/presentation/presentation-info.state';
import { useAtom, useAtomValue } from 'jotai';

import { nextElementAtom } from '@/shared/states/items/next-items.state';
import { useCanvasConfig, useContainerWidth, useTransformer, useCanvasZoomPan } from '..';
import { useDebouncedUpsertElement } from '../../../element/manage-element.hook';
import { useCanvasInteractions } from '../../interactions';
import { useCanvasKeyboard } from '../../keyboards/keyboards.hook';
import { useZoomPanKeyboard } from '../../keyboards/zoom-pan-keyboard.hook';

export const useCanvasEditor = () => {
	const { containerRef, width: containerWidth, height: containerHeight } = useContainerWidth();
	const presentation = useAtomValue(presentationInfoAtom);
	const { scale, canvasWidth, canvasHeight, editableAreaWidth, editableAreaHeight, padding } = useCanvasConfig(
		presentation,
		containerWidth,
		containerHeight,
	);
	const [nextElement, setNextElement] = useAtom(nextElementAtom);
	const { transformerRef, stageRef } = useTransformer();

	// Zoom and Pan functionality
	const zoomPan = useCanvasZoomPan({
		stageRef,
		canvasWidth,
		canvasHeight,
		editableAreaWidth,
		editableAreaHeight,
		padding,
	});

	const canvasDimensions = {
		width: editableAreaWidth,
		height: editableAreaHeight,
	};

	const interactions = useCanvasInteractions({
		nextElement,
		setNextElement,
		scale,
		transformerRef,
		canvasWidth: canvasDimensions.width,
		canvasHeight: canvasDimensions.height,
		padding,
		zoomPan,
	});

	useDebouncedUpsertElement(300);
	useCanvasKeyboard();
	useZoomPanKeyboard({ zoomPan, containerRef });

	return {
		containerRef,
		stageRef,
		containerWidth,
		containerHeight,
		canvasHeight,
		canvasWidth,
		editableAreaWidth,
		editableAreaHeight,
		padding,
		scale,
		zoomPan,
		...interactions,
	};
};
