import { useEffect } from 'react';
import { IUseCanvasZoomPan } from '../config/features/zoom-pan.hook';

interface UseZoomPanKeyboardProps {
	zoomPan: IUseCanvasZoomPan;
	containerRef: React.RefObject<HTMLDivElement>;
}

export const useZoomPanKeyboard = ({ zoomPan, containerRef }: UseZoomPanKeyboardProps) => {
	useEffect(() => {
		const handleKeyDown = (e: KeyboardEvent) => {
			// Only handle keyboard events when the canvas container is focused
			if (!containerRef.current?.contains(document.activeElement)) {
				return;
			}

			// Zoom controls
			if (e.ctrlKey || e.metaKey) {
				switch (e.key) {
					case '=':
					case '+':
						e.preventDefault();
						zoomPan.zoomIn();
						break;
					case '-':
						e.preventDefault();
						zoomPan.zoomOut();
						break;
					case '0':
						e.preventDefault();
						zoomPan.resetAll();
						break;
				}
			}

			// Pan controls (arrow keys)
			if (!e.ctrlKey && !e.metaKey && !e.shiftKey) {
				const panStep = 20;
				const fastPanStep = 100;
				const step = e.altKey ? fastPanStep : panStep;

				switch (e.key) {
					case 'ArrowUp':
						e.preventDefault();
						zoomPan.setPan({
							x: zoomPan.pan.x,
							y: zoomPan.pan.y + step,
						});
						break;
					case 'ArrowDown':
						e.preventDefault();
						zoomPan.setPan({
							x: zoomPan.pan.x,
							y: zoomPan.pan.y - step,
						});
						break;
					case 'ArrowLeft':
						e.preventDefault();
						zoomPan.setPan({
							x: zoomPan.pan.x + step,
							y: zoomPan.pan.y,
						});
						break;
					case 'ArrowRight':
						e.preventDefault();
						zoomPan.setPan({
							x: zoomPan.pan.x - step,
							y: zoomPan.pan.y,
						});
						break;
				}
			}

			// Space key for panning mode
			if (e.key === ' ' && !e.repeat) {
				e.preventDefault();
				zoomPan.setCanPan(true);
			}
		};

		const handleKeyUp = (e: KeyboardEvent) => {
			// Release panning mode when space is released
			if (e.key === ' ') {
				e.preventDefault();
				zoomPan.setCanPan(false);
				zoomPan.handlePanEnd();
			}
		};

		// Add event listeners
		document.addEventListener('keydown', handleKeyDown);
		document.addEventListener('keyup', handleKeyUp);

		// Cleanup
		return () => {
			document.removeEventListener('keydown', handleKeyDown);
			document.removeEventListener('keyup', handleKeyUp);
		};
	}, [zoomPan, containerRef]);
};
